import React, { useState, useEffect, useRef } from "react";
import { Controller } from "react-hook-form";
import { Card, CardContent, CardHeader } from "@base/card";
import { RadioGroup, RadioGroupItem } from "@base/radio-group";
import { Label } from "@base/label";
import { Checkbox } from "@base/checkbox";
import { Badge } from "@commons/badge";
import { Eye, EyeOff, XIcon } from "lucide-react";
import { usePaymentMethods } from "@/hooks/usePaymentMethods";
import { useTranslation } from "@hooks/useTranslation";
import { PaymentMethodForm } from "@/components/payment-methods/payment-method-form";
import { PaymentMethodType } from "@/types/payment-methods";
import { MOBILE_BANKING_OPTIONS } from "@/pages/checkout/constants";
import { InputField } from "@commons/form/input-field";
import { formatCreditCard, formatExpiryDate } from "@/utils/mask";
import type { PaymentMethodProps } from "./types";
import type {
  CreditCardFormData,
  TrueMoneyFormData,
  AlipayFormData,
  CreditDebitCard,
} from "@/types/payment-methods";
import {
  handleCardClick,
  getPaymentMethodErrorMessage,
  getPaymentMethodIconPath,
  getMobileBankingIconPath,
  getBrandIconPath,
  animateCardExpand,
} from "./helpers";

const PaymentMethod: React.FC<PaymentMethodProps> = ({
  control,
  errors,
  watch,
}) => {
  const { t } = useTranslation();
  const { paymentMethods, addPaymentMethod } = usePaymentMethods();

  const creditCards = paymentMethods.filter(
    (method) => method.type === "credit_card" || method.type === "debit_card",
  );

  const [showAddForm, setShowAddForm] = useState(false);
  const [selectedType, setSelectedType] = useState<
    PaymentMethodType | undefined
  >();
  const [showCVV, setShowCVV] = useState(false);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const cardContentRef = useRef<HTMLDivElement>(null);
  const trueMoneyContentRef = useRef<HTMLDivElement>(null);
  const alipayContentRef = useRef<HTMLDivElement>(null);

  const [creditCardForm, setCreditCardForm] = useState<CreditCardFormData>({
    holderName: "",
    cardNumber: "",
    expiryMonth: "",
    expiryYear: "",
    expiryDate: "",
    cvv: "",
    isDefault: false,
    displayName: "",
    omiseToken: "",
  });

  const [trueMoneyForm, setTrueMoneyForm] = useState({
    phoneNumber: "",
    isDefault: false,
    displayName: "",
  });

  const [alipayForm, setAlipayForm] = useState({
    accountEmail: "",
    isDefault: false,
    displayName: "",
  });

  const watchPaymentMethod = (watch as any)("paymentMethod");
  useEffect(() => {
    if (cardContentRef.current && watchPaymentMethod === "card_payment") {
      animateCardExpand(cardContentRef.current);
    }
    if (trueMoneyContentRef.current && watchPaymentMethod === "truemoney") {
      animateCardExpand(trueMoneyContentRef.current);
    }
    if (alipayContentRef.current && watchPaymentMethod === "alipay") {
      animateCardExpand(alipayContentRef.current);
    }
  }, [watchPaymentMethod]);

  const renderPaymentIcon = (type: string, alt?: string) => {
    const iconPath = getPaymentMethodIconPath(type);
    return (
      <img
        src={iconPath}
        alt={alt || type}
        className="h-12 w-12 rounded-lg object-contain"
      />
    );
  };
  const handleCreditCardChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, checked, type: inputType } = e.target;

    setCreditCardForm((prev) => {
      const newData = {
        ...prev,
        [name]: inputType === "checkbox" ? checked : value,
      };

      if (name === "cardNumber") {
        const formattedValue = formatCreditCard(value);
        newData.cardNumber = formattedValue;
      } else if (name === "expiryDate") {
        newData.expiryDate = formatExpiryDate(value);
        const [month, year] = newData.expiryDate.split("/");
        newData.expiryMonth = month || "";
        newData.expiryYear = year || "";
      } else if (name === "cvv") {
        const cleanValue = value.replace(/\D/g, "");
        newData.cvv = cleanValue.slice(0, 4);
      } else if (name === "holderName") {
        newData.holderName = value.replace(/\s+/g, " ").trim();
      }

      return newData;
    });

    if (formErrors[name]) {
      setFormErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  const toggleCVVVisibility = () => {
    setShowCVV(!showCVV);
  };

  const handleTrueMoneyChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, checked, type: inputType } = e.target;

    setTrueMoneyForm((prev) => {
      const newData = {
        ...prev,
        [name]: inputType === "checkbox" ? checked : value,
      };

      if (name === "phoneNumber") {
        const cleanValue = value.replace(/\D/g, "");
        if (cleanValue.length <= 10) {
          let formatted = cleanValue;
          if (cleanValue.length > 3) {
            formatted = `${cleanValue.slice(0, 3)}-${cleanValue.slice(3)}`;
          }
          if (cleanValue.length > 6) {
            formatted = `${cleanValue.slice(0, 3)}-${cleanValue.slice(3, 6)}-${cleanValue.slice(6)}`;
          }
          newData.phoneNumber = formatted;
        } else {
          newData.phoneNumber = prev.phoneNumber;
        }
      }

      return newData;
    });

    if (formErrors[name]) {
      setFormErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  const handleAlipayChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, checked, type: inputType } = e.target;

    setAlipayForm((prev) => {
      const newData = {
        ...prev,
        [name]: inputType === "checkbox" ? checked : value,
      };

      if (name === "accountEmail") {
        newData.accountEmail = value.trim().toLowerCase();
      }

      return newData;
    });

    if (formErrors[name]) {
      setFormErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };
  const handleFormSubmit = async (
    type: PaymentMethodType,
    formData: CreditCardFormData | TrueMoneyFormData | AlipayFormData,
  ) => {
    try {
      await addPaymentMethod(type, formData);
      setShowAddForm(false);
      setSelectedType(undefined);
    } catch (error) {
      console.error("Error adding payment method:", error);
    }
  };

  const handleFormCancel = () => {
    setShowAddForm(false);
    setSelectedType(undefined);
  };

  if (showAddForm && selectedType) {
    return (
      <div>
        <PaymentMethodForm
          type={selectedType}
          isEdit={false}
          onSubmit={handleFormSubmit}
          onCancel={handleFormCancel}
        />
      </div>
    );
  }

  return (
    <div>
      <h2 className="text-[23px] font-medium text-black">
        {t("payment.methods.title")}
      </h2>
      <p className="mb-4 text-sm text-gray-500">
        การทำธุรกรรมทั้งหมดปลอดภัยและมีการเข้ารหัส
      </p>

      <div className="space-y-4">
        <Controller
          name={"paymentMethod" as any}
          control={control as any}
          render={({ field }) => (
            <RadioGroup
              value={field.value as string}
              onValueChange={(value) => {
                field.onChange(value);
              }}
            >
              {/* Saved Credit/Debit Cards */}
              {creditCards.length > 0 && (
                <div className="space-y-3">
                  <h3 className="text-sm font-medium text-gray-700">
                    บัตรเครดิต/เดบิตที่บันทึกไว้
                  </h3>
                  {creditCards.map((method) => (
                    <Card
                      key={`saved_${method.id}`}
                      className={`cursor-pointer transition-colors ${
                        field.value === `saved_${method.id}`
                          ? "border-primary bg-primary/5"
                          : "border-gray-200"
                      }`}
                      onClick={(e) =>
                        handleCardClick(e, field.onChange, `saved_${method.id}`)
                      }
                    >
                      <CardContent className="p-4">
                        <div className="flex items-center space-x-3">
                          <RadioGroupItem
                            value={`saved_${method.id}`}
                            id={`saved_${method.id}`}
                          />
                          <div className="flex items-center space-x-3">
                            <img
                              src={getBrandIconPath(
                                (method as CreditDebitCard).cardBrand,
                              )}
                              alt={method.displayName}
                              className="h-12 w-12 rounded-lg object-contain"
                            />
                            <div className="flex-1">
                              <div className="flex items-center gap-2">
                                <Label
                                  htmlFor={`saved_${method.id}`}
                                  className="cursor-pointer font-medium"
                                >
                                  {method.displayName}
                                </Label>
                                {method.isDefault && (
                                  <Badge variant="outline" className="text-xs">
                                    {t("payment.methods.main")}
                                  </Badge>
                                )}
                              </div>
                              <p className="text-sm text-gray-500">
                                {`${(method as CreditDebitCard).expiryMonth}/${(method as CreditDebitCard).expiryYear.slice(-2)}`}
                              </p>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}

              {/* New Payment Method Options */}
              <div className="space-y-3">
                {/* Credit/Debit Card */}
                <Card
                  className={`cursor-pointer rounded-[16px] py-0 transition-colors hover:shadow-md ${
                    field.value === "card_payment"
                      ? "border-primary-border-card bg-primary/5"
                      : "border-primary-border-card hover:border-gray-300"
                  }`}
                  onClick={(e) =>
                    handleCardClick(e, field.onChange, "card_payment")
                  }
                >
                  <CardHeader
                    className={
                      field.value === "card_payment"
                        ? "border-primary rounded-t-[16px] border pt-3 pb-2"
                        : "cursor-pointer rounded-[16px] py-6 md:shadow-sm"
                    }
                  >
                    <div className="flex items-center space-x-3">
                      <RadioGroupItem
                        value="card_payment"
                        id="card_payment"
                      />
                      <div className="flex items-center space-x-3">
                        {renderPaymentIcon("credit_card", "บัตรเครดิต/เดบิต")}
                        <Label
                          htmlFor="card_payment"
                          className="cursor-pointer font-medium"
                        >
                          บัตรเครดิต/เดบิต
                        </Label>
                      </div>
                    </div>
                  </CardHeader>
                  {field.value === "card_payment" && (
                    <CardContent ref={cardContentRef} className="pt-0 pb-4">
                      <div className="space-y-4">
                        {/* Card Holder Name */}
                        <div className="relative">
                          <InputField
                            id="holderName"
                            name="holderName"
                            placeholder="ชื่อผู้ถือบัตร"
                            value={creditCardForm.holderName}
                            onChange={handleCreditCardChange}
                            error={formErrors.holderName}
                            className="pr-10"
                          />
                          {creditCardForm.holderName && (
                            <button
                              type="button"
                              onClick={() =>
                                setCreditCardForm((prev) => ({
                                  ...prev,
                                  holderName: "",
                                }))
                              }
                              className="absolute top-[18px] right-3 -translate-y-1/2 rounded-full p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-500"
                            >
                              <XIcon className="h-4 w-4" />
                            </button>
                          )}
                        </div>

                        {/* Card Number */}
                        <div className="relative">
                          <InputField
                            id="cardNumber"
                            name="cardNumber"
                            placeholder="หมายเลขบัตร"
                            value={creditCardForm.cardNumber}
                            onChange={handleCreditCardChange}
                            error={formErrors.cardNumber}
                            className="pr-10"
                            maxLength={19}
                          />
                          {creditCardForm.cardNumber && (
                            <button
                              type="button"
                              onClick={() =>
                                setCreditCardForm((prev) => ({
                                  ...prev,
                                  cardNumber: "",
                                }))
                              }
                              className="absolute top-[18px] right-3 -translate-y-1/2 rounded-full p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-500"
                            >
                              <XIcon className="h-4 w-4" />
                            </button>
                          )}
                        </div>

                        {/* Expiry Date and CVV */}
                        <div className="mb-0 grid grid-cols-2 gap-4">
                          <div className="relative">
                            <InputField
                              id="expiryDate"
                              name="expiryDate"
                              placeholder="MM/YY"
                              value={creditCardForm.expiryDate}
                              onChange={handleCreditCardChange}
                              error={formErrors.expiryDate}
                              maxLength={5}
                            />
                          </div>
                          <div className="relative">
                            <InputField
                              id="cvv"
                              name="cvv"
                              type={showCVV ? "text" : "password"}
                              placeholder="CVV"
                              value={creditCardForm.cvv}
                              onChange={handleCreditCardChange}
                              error={formErrors.cvv}
                              className="pr-10"
                              maxLength={4}
                            />
                            <button
                              type="button"
                              onClick={toggleCVVVisibility}
                              className="absolute top-[18px] right-3 -translate-y-1/2 rounded-full p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-500"
                            >
                              {showCVV ? (
                                <EyeOff className="h-4 w-4" />
                              ) : (
                                <Eye className="h-4 w-4" />
                              )}
                            </button>
                          </div>
                        </div>

                        {/* Display Name */}
                        <div className="relative">
                          <InputField
                            id="displayName"
                            name="displayName"
                            placeholder="ชื่อที่แสดง (ไม่บังคับ)"
                            value={creditCardForm.displayName}
                            onChange={handleCreditCardChange}
                            className="pr-10"
                          />
                          {creditCardForm.displayName && (
                            <button
                              type="button"
                              onClick={() =>
                                setCreditCardForm((prev) => ({
                                  ...prev,
                                  displayName: "",
                                }))
                              }
                              className="absolute top-[42px] right-3 -translate-y-1/2 rounded-full p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-500"
                            >
                              <XIcon className="h-4 w-4" />
                            </button>
                          )}
                        </div>

                        {/* Set as Default */}
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="creditCardIsDefault"
                            checked={creditCardForm.isDefault}
                            onCheckedChange={(checked) =>
                              setCreditCardForm((prev) => ({
                                ...prev,
                                isDefault: !!checked,
                              }))
                            }
                          />
                          <Label
                            htmlFor="creditCardIsDefault"
                            className="text-sm"
                          >
                            ตั้งเป็นวิธีการชำระเงินหลัก
                          </Label>
                        </div>
                      </div>
                    </CardContent>
                  )}
                </Card>

                {/* Thai QR */}
                <Card
                  className={`cursor-pointer transition-colors ${
                    field.value === "thai_qr"
                      ? "border-primary bg-primary/5"
                      : "border-gray-200"
                  }`}
                  onClick={(e) => handleCardClick(e, field.onChange, "thai_qr")}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-3">
                      <RadioGroupItem value="thai_qr" id="thai_qr" />
                      <div className="flex items-center space-x-3">
                        {renderPaymentIcon("thai_qr", "Thai QR Payment")}
                        <Label
                          htmlFor="thai_qr"
                          className="cursor-pointer font-medium"
                        >
                          Thai QR Payment
                        </Label>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Mobile Banking */}
                <Card
                  className={`cursor-pointer transition-colors lg:hidden ${
                    field.value === "mobile_banking"
                      ? "border-primary bg-primary/5"
                      : "border-gray-200"
                  }`}
                  onClick={(e) =>
                    handleCardClick(e, field.onChange, "mobile_banking")
                  }
                >
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-3">
                      <RadioGroupItem
                        value="mobile_banking"
                        id="mobile_banking"
                      />
                      <div className="flex items-center space-x-3">
                        {renderPaymentIcon("mobile_banking", "Mobile Banking")}
                        <Label
                          htmlFor="mobile_banking"
                          className="cursor-pointer font-medium"
                        >
                          Mobile Banking
                        </Label>
                      </div>
                    </div>

                    {/* Mobile Banking Options */}
                    {field.value === "mobile_banking" && (
                      <div className="mt-4">
                        <p className="mb-2 text-sm text-gray-600">
                          เลือกธนาคาร:
                        </p>
                        <div className="grid grid-cols-2 gap-2">
                          {MOBILE_BANKING_OPTIONS.map((bank) => (
                            <div
                              key={bank}
                              className="flex cursor-pointer items-center space-x-2 rounded bg-gray-50 p-2 text-sm hover:bg-gray-100"
                            >
                              <img
                                src={getMobileBankingIconPath(bank)}
                                alt={bank}
                                className="h-12 w-12 rounded-lg object-contain"
                              />
                              <span>{bank}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* TrueMoney Wallet */}
                <Card
                  className={`cursor-pointer rounded-[16px] py-0 transition-colors hover:shadow-md ${
                    field.value === "truemoney"
                      ? "border-primary-border-card bg-primary/5"
                      : "border-primary-border-card hover:border-gray-300"
                  }`}
                  onClick={(e) =>
                    handleCardClick(e, field.onChange, "truemoney")
                  }
                >
                  <CardHeader
                    className={
                      field.value === "truemoney"
                        ? "border-primary rounded-t-[16px] border pt-3 pb-2"
                        : "cursor-pointer rounded-[16px] py-6 md:shadow-sm"
                    }
                  >
                    <div className="flex items-center space-x-3">
                      <RadioGroupItem
                        value="truemoney"
                        id="truemoney"
                      />
                      <div className="flex items-center space-x-3">
                        {renderPaymentIcon("truemoney", "TrueMoney Wallet")}
                        <Label
                          htmlFor="truemoney"
                          className="cursor-pointer font-medium"
                        >
                          TrueMoney Wallet
                        </Label>
                      </div>
                    </div>
                  </CardHeader>
                  {field.value === "truemoney" && (
                    <CardContent
                      ref={trueMoneyContentRef}
                      className="pt-0 pb-4"
                    >
                      <div className="space-y-4">
                        {/* Phone Number */}
                        <div className="relative">
                          <InputField
                            id="phoneNumber"
                            name="phoneNumber"
                            placeholder="หมายเลขโทรศัพท์ (XXX-XXX-XXXX)"
                            value={trueMoneyForm.phoneNumber}
                            onChange={handleTrueMoneyChange}
                            error={formErrors.phoneNumber}
                            className="pr-10"
                            maxLength={12}
                          />
                          {trueMoneyForm.phoneNumber && (
                            <button
                              type="button"
                              onClick={() =>
                                setTrueMoneyForm((prev) => ({
                                  ...prev,
                                  phoneNumber: "",
                                }))
                              }
                              className="absolute top-[18px] right-3 -translate-y-1/2 rounded-full p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-500"
                            >
                              <XIcon className="h-4 w-4" />
                            </button>
                          )}
                        </div>

                        {/* Display Name */}
                        <div className="relative">
                          <InputField
                            id="truemoneyDisplayName"
                            name="displayName"
                            placeholder="ชื่อที่แสดง (ไม่บังคับ)"
                            value={trueMoneyForm.displayName}
                            onChange={handleTrueMoneyChange}
                            className="pr-10"
                          />
                          {trueMoneyForm.displayName && (
                            <button
                              type="button"
                              onClick={() =>
                                setTrueMoneyForm((prev) => ({
                                  ...prev,
                                  displayName: "",
                                }))
                              }
                              className="absolute top-[18px] right-3 -translate-y-1/2 rounded-full p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-500"
                            >
                              <XIcon className="h-4 w-4" />
                            </button>
                          )}
                        </div>

                        {/* Set as Default */}
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="truemoneyIsDefault"
                            checked={trueMoneyForm.isDefault}
                            onCheckedChange={(checked) =>
                              setTrueMoneyForm((prev) => ({
                                ...prev,
                                isDefault: !!checked,
                              }))
                            }
                          />
                          <Label
                            htmlFor="truemoneyIsDefault"
                            className="text-sm"
                          >
                            ตั้งเป็นวิธีการชำระเงินหลัก
                          </Label>
                        </div>
                      </div>
                    </CardContent>
                  )}
                </Card>

                {/* AliPay */}
                <Card
                  className={`cursor-pointer rounded-[16px] py-0 transition-colors hover:shadow-md ${
                    field.value === "new_alipay"
                      ? "border-primary-border-card bg-primary/5"
                      : "border-primary-border-card hover:border-gray-300"
                  }`}
                  onClick={(e) =>
                    handleCardClick(e, field.onChange, "new_alipay")
                  }
                >
                  <CardHeader
                    className={
                      field.value === "new_alipay"
                        ? "border-primary rounded-t-[16px] border pt-3 pb-2"
                        : "cursor-pointer rounded-[16px] py-6 md:shadow-sm"
                    }
                  >
                    <div className="flex items-center space-x-3">
                      <RadioGroupItem value="new_alipay" id="new_alipay" />
                      <div className="flex items-center space-x-3">
                        {renderPaymentIcon("alipay", "AliPay")}
                        <Label
                          htmlFor="new_alipay"
                          className="cursor-pointer font-medium"
                        >
                          AliPay
                        </Label>
                      </div>
                    </div>
                  </CardHeader>
                  {field.value === "new_alipay" && (
                    <CardContent ref={alipayContentRef} className="pt-0 pb-4">
                      <div className="space-y-4">
                        {/* Account Email */}
                        <div className="relative">
                          <InputField
                            id="accountEmail"
                            name="accountEmail"
                            type="email"
                            placeholder="อีเมลบัญชี AliPay"
                            value={alipayForm.accountEmail}
                            onChange={handleAlipayChange}
                            error={formErrors.accountEmail}
                            className="pr-10"
                          />
                          {alipayForm.accountEmail && (
                            <button
                              type="button"
                              onClick={() =>
                                setAlipayForm((prev) => ({
                                  ...prev,
                                  accountEmail: "",
                                }))
                              }
                              className="absolute top-[18px] right-3 -translate-y-1/2 rounded-full p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-500"
                            >
                              <XIcon className="h-4 w-4" />
                            </button>
                          )}
                        </div>

                        {/* Display Name */}
                        <div className="relative">
                          <InputField
                            id="alipayDisplayName"
                            name="displayName"
                            placeholder="ชื่อที่แสดง (ไม่บังคับ)"
                            value={alipayForm.displayName}
                            onChange={handleAlipayChange}
                            className="pr-10"
                          />
                          {alipayForm.displayName && (
                            <button
                              type="button"
                              onClick={() =>
                                setAlipayForm((prev) => ({
                                  ...prev,
                                  displayName: "",
                                }))
                              }
                              className="absolute top-[18px] right-3 -translate-y-1/2 rounded-full p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-500"
                            >
                              <XIcon className="h-4 w-4" />
                            </button>
                          )}
                        </div>

                        {/* Set as Default */}
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="alipayIsDefault"
                            checked={alipayForm.isDefault}
                            onCheckedChange={(checked) =>
                              setAlipayForm((prev) => ({
                                ...prev,
                                isDefault: !!checked,
                              }))
                            }
                          />
                          <Label htmlFor="alipayIsDefault" className="text-sm">
                            ตั้งเป็นวิธีการชำระเงินหลัก
                          </Label>
                        </div>
                      </div>
                    </CardContent>
                  )}
                </Card>
              </div>
            </RadioGroup>
          )}
        />

        {(errors as any).paymentMethod && (
          <p className="mt-2 text-xs text-red-600">
            {getPaymentMethodErrorMessage((errors as any).paymentMethod)}
          </p>
        )}
      </div>
    </div>
  );
};

export default PaymentMethod;
