import type { Control, FieldErrors, UseFormWatch } from "react-hook-form";

import type { CheckoutFormData, PaymentFormData } from "@/pages/checkout/constants";
import type { BasePaymentMethod } from "@/types/payment-methods";

export interface PaymentMethodProps {
  control: Control<CheckoutFormData> | Control<PaymentFormData>;
  errors: FieldErrors<CheckoutFormData> | FieldErrors<PaymentFormData>;
  watch: UseFormWatch<CheckoutFormData> | UseFormWatch<PaymentFormData>;
}

export interface PaymentOption {
  id: string;
  type: string;
  name: string;
  description?: string;
  icon?: string;
  savedMethod?: BasePaymentMethod;
}
