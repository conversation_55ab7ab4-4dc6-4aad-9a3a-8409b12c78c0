import React from "react";

import { gsap } from "gsap";

import bualuangIcon from "@/assets/images/mobile-banking/bualuang.webp";
import kmaIcon from "@/assets/images/mobile-banking/kma.webp";
import kplusIcon from "@/assets/images/mobile-banking/kplus.webp";
import ktbNextIcon from "@/assets/images/mobile-banking/ktb-next.webp";
// Mobile banking icons
import scbIcon from "@/assets/images/mobile-banking/scb.webp";
import alipayIcon from "@/assets/images/payments/alipay.png";
import amexIcon from "@/assets/images/payments/amex.png";
import jcbIcon from "@/assets/images/payments/jcb.png";
import mastercardIcon from "@/assets/images/payments/mastercard.png";
import paypalIcon from "@/assets/images/payments/paypal.png";
import thaiQrIcon from "@/assets/images/payments/thai-qr.png";
import truemoneyIcon from "@/assets/images/payments/truemoney.png";
// Payment method icons
import visaIcon from "@/assets/images/payments/visa.png";

/**
 * Helper function to handle card clicks without interfering with form elements
 * Prevents double-triggering when clicking on radio buttons or other form elements
 */
export const handleCardClick = (
  e: React.MouseEvent<HTMLDivElement>,
  onChange: (value: string) => void,
  value: string,
) => {
  // Prevent card click when clicking on form elements
  const target = e.target as HTMLElement;
  if (
    target.tagName === "INPUT" ||
    target.tagName === "SELECT" ||
    target.tagName === "TEXTAREA" ||
    target.tagName === "BUTTON" ||
    target.closest(
      'input, select, textarea, button, [role="combobox"], [role="listbox"]',
    )
  ) {
    return;
  }
  onChange(value);
};

/**
 * Gets error message for payment method fields
 */
export const getPaymentMethodErrorMessage = (
  error: { message?: string } | undefined,
): string | undefined => {
  return error?.message;
};

/**
 * Gets the appropriate icon path for a payment method type
 */
export const getPaymentMethodIconPath = (type: string): string => {
  switch (type) {
    case "credit_card":
    case "debit_card":
      return visaIcon; // Default to Visa for generic card
    case "truemoney":
      return truemoneyIcon;
    case "alipay":
      return alipayIcon;
    case "thai_qr":
    case "promptpay":
      return thaiQrIcon;
    case "mobile_banking":
      return kplusIcon;
    case "paypal":
      return paypalIcon;
    case "visa":
      return visaIcon;
    case "mastercard":
      return mastercardIcon;
    case "amex":
      return amexIcon;
    case "jcb":
      return jcbIcon;
    default:
      return visaIcon;
  }
};

/**
 * Gets mobile banking icon path by bank name
 */
export const getMobileBankingIconPath = (bankName: string): string => {
  const bankMap: Record<string, string> = {
    scb: scbIcon,
    kplus: kplusIcon,
    bbm: bualuangIcon,
    ktb: ktbNextIcon,
    kma: kmaIcon,
  };

  return bankMap[bankName] || scbIcon;
};

/**
 * Gets brand icon path from API brand value
 */
export const getBrandIconPath = (brand: string): string => {
  const normalizedBrand = brand.toLowerCase();
  return `src/assets/images/payments/${normalizedBrand}.png`;
};

/**
 * Animates card content expansion with GSAP
 */
export const animateCardExpand = (element: HTMLElement) => {
  gsap.set(element, {
    height: 0,
    opacity: 0,
    overflow: "hidden",
    transformOrigin: "top center",
  });

  gsap.to(element, {
    height: "auto",
    opacity: 1,
    duration: 0.6,
    ease: "power2.out",
    onComplete: () => {
      gsap.set(element, { overflow: "visible" });
    },
  });
};
