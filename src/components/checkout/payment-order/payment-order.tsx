import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@base/button";
import { Badge } from "@commons/badge";
import { ArrowLeft } from "lucide-react";
import { PaymentMethod } from "@/components/checkout";
import { paymentSchema } from "@/pages/checkout/constants";
import type { PaymentFormData } from "@/pages/checkout/constants";
import type { PaymentOrderProps } from "./types";
import { usePayment } from "@/hooks/usePayment";
import { useOrder } from "@/hooks/useOrder";
import { CancelDialog } from "@/components/commons";

import { omiseTokenizationService } from "@/services/payment/omise-tokenization";
import { paymentMethodService } from "@/services/payment/payment-method-service";
import { formatPrice, formatOrderDate } from "./helpers";
import type { CreateOrderResponse } from "@/types/order";
import type { CreditCardFormData } from "@/types/payment-methods";

export const PaymentOrder: React.FC<PaymentOrderProps> = ({
  order,
  onBackToCreateOrder,
  onPaymentComplete,
}) => {
  const [currentOrder, setCurrentOrder] = useState<CreateOrderResponse>(order);
  const [isLoadingOrder, setIsLoadingOrder] = useState(false);
  const [showSaveCardDialog, setShowSaveCardDialog] = useState(false);
  const [pendingPaymentData, setPendingPaymentData] = useState<{
    formData: PaymentFormData;
    token: string;
    creditCardData: CreditCardFormData;
  } | null>(null);

  const {
    isProcessingPayment,
    paymentError,
    processPaymentFromForm,
    clearPaymentError,
  } = usePayment();

  const { getOrderById } = useOrder();

  const {
    control,
    handleSubmit,
    watch,
    formState: { errors, isSubmitting },
  } = useForm<PaymentFormData>({
    resolver: zodResolver(paymentSchema),
    defaultValues: {
      paymentMethod: "card_payment",
    },
  });

  useEffect(() => {
    const fetchOrderDetails = async () => {
      if (!order || !order.id) {
        return;
      }

      setIsLoadingOrder(true);

      try {
        const freshOrder = await getOrderById(order.id);
        if (freshOrder) {
          setCurrentOrder(freshOrder);
        }
      } catch {
        /* ignore error */
      } finally {
        setIsLoadingOrder(false);
      }
    };

    fetchOrderDetails();
  }, [order, getOrderById]);

  const onSubmit = async (data: PaymentFormData) => {
    try {
      clearPaymentError();

      let cardProfileId: number | undefined;

      if (data.paymentMethod === "card_payment") {
        if (data.cardProfileId) {
          cardProfileId = data.cardProfileId;
        } else {
          const holderNameElement = document.getElementById(
            "holderName",
          ) as HTMLInputElement;
          const cardNumberElement = document.getElementById(
            "cardNumber",
          ) as HTMLInputElement;
          const expiryDateElement = document.getElementById(
            "expiryDate",
          ) as HTMLInputElement;
          const cvvElement = document.getElementById("cvv") as HTMLInputElement;

          if (
            holderNameElement &&
            cardNumberElement &&
            expiryDateElement &&
            cvvElement
          ) {
            const [expiryMonth, expiryYear] =
              expiryDateElement.value.split("/");
            const creditCardData = {
              holderName: holderNameElement.value,
              cardNumber: cardNumberElement.value,
              expiryMonth: expiryMonth || "",
              expiryYear: expiryYear ? `20${expiryYear}` : "",
              cvv: cvvElement.value,
            };

            try {
              const tokenResponse =
                await omiseTokenizationService.createCardToken(creditCardData);

              const creditCardFormData: CreditCardFormData = {
                holderName: creditCardData.holderName,
                cardNumber: creditCardData.cardNumber,
                expiryMonth: creditCardData.expiryMonth,
                expiryYear: creditCardData.expiryYear,
                expiryDate: `${creditCardData.expiryMonth}/${creditCardData.expiryYear.slice(-2)}`,
                cvv: creditCardData.cvv,
                isDefault: false,
                displayName: `Card ending in ${creditCardData.cardNumber.slice(-4)}`,
                omiseToken: tokenResponse,
              };

              setPendingPaymentData({
                formData: data,
                token: tokenResponse,
                creditCardData: creditCardFormData,
              });

              setShowSaveCardDialog(true);
              return;
            } catch {
              throw new Error("Failed to create and save payment card");
            }
          } else {
            throw new Error(
              "Credit card information is required for new card payment",
            );
          }
        }
      }

      const paymentResponse = await processPaymentFromForm(
        currentOrder.id,
        data,
        cardProfileId,
        undefined,
      );

      if (paymentResponse) {
        onPaymentComplete();
      }
    } catch {
      /* ignore error */
    }
  };

  const handleSaveCardConfirm = async () => {
    if (!pendingPaymentData) return;

    try {
      setShowSaveCardDialog(false);

      const savedCard = await paymentMethodService.addCreditCard(
        pendingPaymentData.creditCardData,
      );
      const cardProfileId = parseInt(savedCard.id);

      const paymentResponse = await processPaymentFromForm(
        currentOrder.id,
        pendingPaymentData.formData,
        cardProfileId,
        undefined,
      );

      if (paymentResponse) {
        onPaymentComplete();
      }

      setPendingPaymentData(null);
    } catch {
      setPendingPaymentData(null);
    }
  };

  const handleSaveCardCancel = async () => {
    if (!pendingPaymentData) return;

    try {
      setShowSaveCardDialog(false);

      const paymentResponse = await processPaymentFromForm(
        currentOrder.id,
        pendingPaymentData.formData,
        undefined,
        pendingPaymentData.token,
      );

      if (paymentResponse) {
        onPaymentComplete();
      }

      setPendingPaymentData(null);
    } catch {
      setPendingPaymentData(null);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={onBackToCreateOrder}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          กลับไปแก้ไข
        </Button>
        <h1 className="text-[32px] font-medium">ชำระเงิน</h1>
      </div>

      <div className="rounded-lg border bg-white p-6 shadow-sm">
        <div className="mb-4 flex items-center justify-between">
          <h2 className="text-lg font-semibold">
            คำสั่งซื้อ #{currentOrder.id}
          </h2>
          <Badge variant="outline" className="text-xs">
            สร้างเมื่อ {formatOrderDate(currentOrder.created_at)}
          </Badge>
        </div>

        {isLoadingOrder ? (
          <div className="flex items-center justify-center py-8">
            <div className="text-sm text-gray-500">
              กำลังโหลดข้อมูลคำสั่งซื้อ...
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="flex items-center justify-between border-b py-2">
              <span className="font-medium">ยอดรวม</span>
              <span className="text-primary text-lg font-semibold">
                {formatPrice(currentOrder.total_price)}
              </span>
            </div>
          </div>
        )}
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <PaymentMethod control={control} errors={errors} watch={watch} />

        {paymentError && (
          <div className="rounded-lg border border-red-200 bg-red-50 p-4">
            <p className="text-sm text-red-600">{paymentError}</p>
          </div>
        )}

        <Button
          type="submit"
          size="lg"
          className="w-full"
          disabled={isSubmitting || isProcessingPayment}
        >
          {isProcessingPayment
            ? "กำลังประมวลผลการชำระเงิน..."
            : isSubmitting
              ? "กำลังดำเนินการ..."
              : `ชำระเงิน ${formatPrice(currentOrder.total_price)}`}
        </Button>
      </form>

      <CancelDialog
        open={showSaveCardDialog}
        onOpenChange={setShowSaveCardDialog}
        onCancel={handleSaveCardCancel}
        onConfirm={handleSaveCardConfirm}
        cancelText="ไม่บันทึก"
        confirmText="บันทึกบัตร"
        description="คุณต้องการบันทึกบัตรใหม่หรือไม่?"
      />
    </div>
  );
};
