import React, { useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle,
  CardContent,
} from "@/components/commons/base/card";
import { Badge } from "@/components/commons/badge";
import { useTranslation } from "@/hooks/useTranslation";
import { CreditCardIcon, Trash2 } from "lucide-react";
import { getPaymentMethodIcon } from "./helpers";
import { PaymentMethodForm } from "./payment-method-form";
import { usePaymentMethods } from "@/hooks/usePaymentMethods";
import { Spinner } from "@/components/commons/loading-spinner";
import { CancelDialog } from "@/components/commons";
import { useToast } from "@/components/commons/toast";
import {
  PaymentMethodType,
  type CreditDebitCard,
  type CreditCardFormData,
  type TrueMoneyFormData,
  type AlipayFormData,
  type BasePaymentMethod,
} from "@/types/payment-methods";
import type { PaymentMethodsProps } from "@components/payment-methods/types";

export const PaymentMethods: React.FC<PaymentMethodsProps> = ({
  paymentMethods: propPaymentMethods = [],
}) => {
  const { t } = useTranslation();
  const { toast } = useToast();
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [selectedType, setSelectedType] = useState<
    PaymentMethodType | undefined
  >(undefined);
  const [selectedMethod, setSelectedMethod] = useState<
    BasePaymentMethod | undefined
  >(undefined);
  const [isEdit, setIsEdit] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [methodToDelete, setMethodToDelete] = useState<
    BasePaymentMethod | undefined
  >(undefined);

  // Use the payment methods hook
  const {
    paymentMethods: hookPaymentMethods,
    isLoading,
    error,
    addPaymentMethod,
    removeMethod,
  } = usePaymentMethods();

  // Use payment methods from props if provided, otherwise use from hook
  const displayPaymentMethods =
    propPaymentMethods.length > 0 ? propPaymentMethods : hookPaymentMethods;

  const handleAddMethod = (type: PaymentMethodType) => {
    setSelectedType(type);
    setSelectedMethod(undefined);
    setIsEdit(false);
    setIsFormOpen(true);
  };

  const handleEditMethod = (method: BasePaymentMethod) => {
    setSelectedMethod(method);
    setSelectedType(method.type);
    setIsEdit(true);
    setIsFormOpen(true);
  };

  const handleFormCancel = () => {
    setIsFormOpen(false);
    setSelectedMethod(undefined);
    setSelectedType(undefined);
  };

  const handleFormSubmit = async (
    type: PaymentMethodType,
    data: CreditCardFormData | TrueMoneyFormData | AlipayFormData,
  ) => {
    try {
      await addPaymentMethod(type, data);
      setIsFormOpen(false);
    } catch (error) {
      console.error("Error saving payment method:", error);
      // Handle error (show toast, etc.)
    }
  };

  // Handle delete payment method
  const handleDeleteMethod = (method: BasePaymentMethod) => {
    setMethodToDelete(method);
    setShowDeleteDialog(true);
  };

  const handleDeleteCancel = () => {
    setShowDeleteDialog(false);
    setMethodToDelete(undefined);
  };

  const handleDeleteConfirm = async () => {
    if (!methodToDelete) return;

    try {
      await removeMethod(methodToDelete.id);
      setShowDeleteDialog(false);
      setMethodToDelete(undefined);
      toast({
        title: t("payment.methods.deleteSuccess"),
        variant: "default",
      });
    } catch (error) {
      console.error("Error deleting payment method:", error);
      toast({
        title: t("payment.methods.deleteError"),
        description: (error as Error).message,
        variant: "destructive",
      });
    }
  };

  if (isFormOpen && selectedType) {
    return (
      <PaymentMethodForm
        type={selectedType}
        initialData={selectedMethod}
        isEdit={isEdit}
        onSubmit={handleFormSubmit}
        onCancel={handleFormCancel}
      />
    );
  }

  return (
    <Card className="border-primary-border-card w-full sm:border-none md:border">
      <CardHeader>
        <CardTitle className="font-regular text-[23px] text-black">
          {t("payment.methods.title")}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div>
            <h3 className="mb-4 text-base font-medium">
              {t("payment.methods.current")}
            </h3>
            <div className="space-y-4">
              {isLoading ? (
                <div className="p-4 text-center">
                  <Spinner size="medium" />
                </div>
              ) : error ? (
                <div className="p-4 text-center text-red-500">
                  <p>{t("payment.methods.error")}</p>
                </div>
              ) : displayPaymentMethods.length === 0 ? (
                <div className="p-4 text-center">
                  <p>{t("payment.methods.empty")}</p>
                </div>
              ) : (
                displayPaymentMethods.map((method) => (
                  <div
                    key={method.id}
                    className="border-primary-border-card flex items-center rounded-lg border p-4 lg:w-1/2"
                  >
                    <div className="mr-4 flex h-10 w-10 items-center justify-center">
                      {getPaymentMethodIcon(method, t)}
                    </div>
                    <div
                      className="flex-1 cursor-pointer"
                      onClick={() => handleEditMethod(method)}
                    >
                      <div className="flex items-center justify-between">
                        <p className="font-regular">{method.displayName}</p>
                        {method.isDefault && (
                          <Badge variant="outline" className="ml-2">
                            {t("payment.methods.main")}
                          </Badge>
                        )}
                      </div>
                      {(method.type === PaymentMethodType.CREDIT_CARD ||
                        method.type === PaymentMethodType.DEBIT_CARD) && (
                        <p className="text-sm text-gray-600">
                          {`${(method as CreditDebitCard).expiryMonth}/${(method as CreditDebitCard).expiryYear.toString().slice(-2)}`}
                        </p>
                      )}
                    </div>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteMethod(method);
                      }}
                      className="ml-2 flex h-8 w-8 items-center justify-center rounded-full text-red-500 transition-colors hover:bg-red-50"
                      title={t("payment.methods.delete")}
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                ))
              )}
            </div>
          </div>

          <div>
            <h3 className="mb-4 text-base font-medium">
              {t("payment.methods.add")}
            </h3>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-1">
              <button
                className="border-primary-border-card flex items-center rounded-lg border p-4 transition-colors hover:bg-gray-50 lg:w-1/2"
                onClick={() => handleAddMethod(PaymentMethodType.CREDIT_CARD)}
              >
                <div className="mr-4 flex h-10 w-10 items-center justify-center">
                  <CreditCardIcon className="h-5 w-5" />
                </div>
                <span>{t("payment.methods.addCredit")}</span>
              </button>

              {/*<button*/}
              {/*  className="border-primary-border-card flex items-center rounded-lg border p-4 transition-colors hover:bg-gray-50 lg:w-1/2"*/}
              {/*  onClick={() => handleAddMethod(PaymentMethodType.TRUEMONEY)}*/}
              {/*>*/}
              {/*  <div className="mr-4 flex h-10 w-10 items-center justify-center">*/}
              {/*    <img*/}
              {/*      src="src/assets/images/payments/truemoney.png"*/}
              {/*      alt={t("payment.methods.altText.truemoney")}*/}
              {/*      className="h-8 w-8 object-contain"*/}
              {/*    />*/}
              {/*  </div>*/}
              {/*  <span>{t("payment.methods.types.truemoney")}</span>*/}
              {/*</button>*/}

              {/*<button*/}
              {/*  className="border-primary-border-card flex items-center rounded-lg border p-4 transition-colors hover:bg-gray-50 lg:w-1/2"*/}
              {/*  onClick={() => handleAddMethod(PaymentMethodType.ALIPAY)}*/}
              {/*>*/}
              {/*  <div className="mr-4 flex h-10 w-10 items-center justify-center">*/}
              {/*    <img*/}
              {/*      src="src/assets/images/payments/alipay.png"*/}
              {/*      alt={t("payment.methods.altText.alipay")}*/}
              {/*      className="h-5 w-5 object-contain"*/}
              {/*    />*/}
              {/*  </div>*/}
              {/*  <span>{t("payment.methods.types.alipay")}</span>*/}
              {/*</button>*/}
            </div>
          </div>
        </div>
      </CardContent>

      <CancelDialog
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        onCancel={handleDeleteCancel}
        onConfirm={handleDeleteConfirm}
        cancelText={t("payment.methods.cancel")}
        confirmText={t("payment.methods.delete")}
        description={t("payment.methods.deleteConfirmation")}
      />
    </Card>
  );
};
