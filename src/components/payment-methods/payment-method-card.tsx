import React from "react";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/commons/base/button";
import { Badge } from "@commons/badge";
import {
  type PaymentMethod,
  PaymentMethodType,
  type CreditDebitCard,
} from "@/types/payment-methods";
import { Edit, Trash } from "lucide-react";
import { useTranslation } from "@/hooks/useTranslation";
import { getBrandIconPath } from "@/components/checkout/payment-method/helpers";

interface PaymentMethodCardProps {
  method: PaymentMethod;
  onSetDefault: (id: string) => void;
  onRemove: (id: string) => void;
  onEdit?: (id: string) => void;
}

export const PaymentMethodCard: React.FC<PaymentMethodCardProps> = ({
  method,
  onSetDefault,
  onRemove,
  onEdit,
}) => {
  const { t } = useTranslation();

  const getIcon = () => {
    switch (method.type) {
      case PaymentMethodType.CREDIT_CARD:
      case PaymentMethodType.DEBIT_CARD: {
        const card = method as CreditDebitCard;
        // Match payment-method component style - larger icon
        return (
          <img
            src={getBrandIconPath(card.cardBrand)}
            alt={method.displayName}
            className="h-12 w-12 rounded-lg object-contain"
          />
        );
      }
      case PaymentMethodType.TRUEMONEY:
        return (
          <img
            src="src/assets/images/payments/truemoney.png"
            alt={t("payment.methods.altText.truemoney")}
            className="h-12 w-12 rounded-lg object-contain"
          />
        );
      case PaymentMethodType.ALIPAY:
        return (
          <img
            src="src/assets/images/payments/alipay.png"
            alt={t("payment.methods.altText.alipay")}
            className="h-12 w-12 rounded-lg object-contain"
          />
        );
      default:
        return <div className="h-12 w-12 rounded-lg bg-gray-200" />;
    }
  };

  return (
    <div
      className={cn(
        "cursor-pointer rounded-lg border p-4 transition-colors",
        method.isDefault ? "border-primary bg-primary/5" : "border-gray-200",
      )}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          {getIcon()}
          <div className="flex-1">
            <div className="flex items-center gap-2">
              <span className="cursor-pointer font-medium">
                {method.displayName}
              </span>
              {method.isDefault && (
                <Badge variant="outline" className="text-xs">
                  {t("payment.methods.main")}
                </Badge>
              )}
            </div>
            {(method.type === PaymentMethodType.CREDIT_CARD ||
              method.type === PaymentMethodType.DEBIT_CARD) && (
              <p className="text-sm text-gray-500">
                {`${(method as CreditDebitCard).expiryMonth}/${(method as CreditDebitCard).expiryYear.slice(-2)}`}
              </p>
            )}
            {method.lastUsed && (
              <p className="text-xs text-gray-400">
                {t("payment.methods.lastUsed", {
                  date: new Date(method.lastUsed).toLocaleDateString(),
                })}
              </p>
            )}
          </div>
        </div>

        <div className="flex space-x-2">
          {!method.isDefault && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => onSetDefault(method.id)}
            >
              {t("payment.methods.setDefault")}
            </Button>
          )}

          {onEdit && (
            <Button variant="ghost" size="sm" onClick={() => onEdit(method.id)}>
              <Edit className="h-4 w-4" />
            </Button>
          )}

          <Button
            variant="ghost"
            size="sm"
            onClick={() => onRemove(method.id)}
            className="text-destructive"
          >
            <Trash className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};
