import type { PaymentFormData, PaymentData } from "@/pages/checkout/constants";
import {ApiService, PAYMENT_ENDPOINTS} from "@/services/api";
import type {
  PaymentMethodData,
  ProcessPaymentRequest,
  ProcessPaymentResponse,
} from "@/types/order";

class PaymentService {
  mapPaymentData(
    orderId: number,
    paymentData: PaymentData,
  ): ProcessPaymentRequest {
    return {
      order_id: orderId,
      payment_method: paymentData.paymentMethod,
      card_profile_id: paymentData.cardProfileId,
      token: paymentData.token,
      source: paymentData.source,
      mobile_bank: paymentData.mobileBank,
      return_uri: paymentData.returnUri,
    };
  }

  // Legacy method for backward compatibility
  mapPaymentDataLegacy(
    orderId: number,
    formData: PaymentFormData,
    cardProfileId?: number,
    token?: string,
  ): ProcessPaymentRequest {
    return {
      order_id: orderId,
      payment_method: formData.paymentMethod,
      card_profile_id: cardProfileId,
      token: token,
    };
  }

  async processPayment(
    paymentData: ProcessPaymentRequest,
  ): Promise<ProcessPaymentResponse> {
    try {
      const response = await ApiService.post<ProcessPaymentResponse>(
        PAYMENT_ENDPOINTS.PAYMENT,
        paymentData,
      );
      return response;
    } catch (error) {
      throw new Error(`Failed to process payment: ${(error as Error).message}`);
    }
  }

  parsePaymentMethod(
    paymentMethodString: string,
    cardProfileId?: number,
    token?: string,
  ): PaymentMethodData {
    return {
      method: paymentMethodString,
      card_profile_id: cardProfileId,
      token: token,
    };
  }
}

export const paymentService = new PaymentService();
