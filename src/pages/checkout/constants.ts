import { z } from "zod";

export const PAYMENT_METHODS = [
  {
    id: "card_payment",
    type: "card_payment" as const,
    name: "Credit or Debit Card",
  },
  {
    id: "mobile_banking",
    type: "mobile_banking" as const,
    name: "Mobile Banking",
  },
  {
    id: "truemoney",
    type: "truemoney" as const,
    name: "<PERSON><PERSON><PERSON>",
  },
  {
    id: "alipay",
    type: "alipay" as const,
    name: "<PERSON><PERSON><PERSON>",
  },
  {
    id: "thai_qr",
    type: "thai_qr" as const,
    name: "Thai QR Payment",
  },
];

// Payment method types for API
export type PaymentMethodApiType =
  | "card_payment"
  | "mobile_banking"
  | "truemoney"
  | "alipay"
  | "thai_qr";

// Payment data interface for different payment methods
export interface PaymentData {
  paymentMethod: PaymentMethodApiType;
  cardProfileId?: number;
  token?: string;
  source?: string;
  mobileBank?: string;
  returnUri?: string;
}

export const MOBILE_BANKING_OPTIONS = ["bbm", "kplus", "ktb", "kma", "scb"];

export const checkoutSchema = z.object({
  contactInfo: z.object({
    email: z.string().email("กรุณาป้อนอีเมลที่ถูกต้อง"),
  }),
  shippingAddress: z.object({
    firstName: z.string().min(1, ""),
    lastName: z.string().min(1, ""),
    company: z.string().optional(),
    address_line1: z.string().min(1, "กรุณาป้อนที่อยู่"),
    address_line2: z.string().optional(),
    sub_district: z.string().min(1, "กรุณาป้อนตำบล"),
    district: z.string().min(1, "กรุณาป้อนอำเภอ"),
    city: z.string().min(1, "กรุณาป้อนเมือง"),
    postal_code: z.string().min(5, "กรุณาป้อนรหัสไปรษณีย์"),
    province: z.string().min(1, "กรุณาเลือกจังหวัด"),
    country: z.string().min(1, "กรุณาเลือกประเทศ"),
  }),
  taxInvoice: z
    .object({
      wantTaxInvoice: z.boolean(),
      personalInfo: z
        .object({
          type: z.enum(["individual", "company"]),
          firstName: z.string().optional(),
          lastName: z.string().optional(),
          companyName: z.string().optional(),
          email: z.string().optional(),
          phone: z.string().optional(),
        })
        .optional(),
      taxInfo: z
        .object({
          taxId: z.string().optional(),
          address_line1: z.string().optional(),
          address_line2: z.string().optional(),
          sub_district: z.string().optional(),
          district: z.string().optional(),
          city: z.string().optional(),
          postal_code: z.string().optional(),
          province: z.string().optional(),
          country: z.string().optional(),
          useSameAddress: z.boolean(),
        })
        .optional(),
    })
    .refine(
      (data) => {
        // If wantTaxInvoice is false, skip all validation
        if (!data.wantTaxInvoice) {
          return true;
        }

        // If wantTaxInvoice is true, validate required fields
        if (!data.personalInfo) {
          return false;
        }

        const personalInfo = data.personalInfo;

        // Validate email
        if (
          !personalInfo.email ||
          !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(personalInfo.email)
        ) {
          return false;
        }

        // Validate phone
        if (!personalInfo.phone || personalInfo.phone.length < 10) {
          return false;
        }

        return true;
      },
      {
        message: "กรุณาป้อนข้อมูลใบกำกับภาษีให้ครบถ้วน",
        path: ["personalInfo"],
      },
    ),
  shippingMethod: z.string().min(1, "กรุณาเลือกวิธีการจัดส่ง"),
  deliveryAddressId: z.number().optional(),
  agreeToTerms: z.boolean().refine((val) => val === true, {
    message: "กรุณายอมรับข้อกำหนดในการให้บริการ",
  }),
});

export type CheckoutFormData = z.infer<typeof checkoutSchema>;

// Payment form schema for payment-order step
export const paymentSchema = z.object({
  paymentMethod: z.enum(["card_payment", "mobile_banking", "truemoney", "alipay", "thai_qr"], {
    errorMap: () => ({ message: "กรุณาเลือกวิธีการชำระเงิน" })
  }),
  cardProfileId: z.number().optional(),
  token: z.string().optional(),
  source: z.string().optional(),
  mobileBank: z.string().optional(),
  returnUri: z.string().optional(),
});

export type PaymentFormData = z.infer<typeof paymentSchema>;
